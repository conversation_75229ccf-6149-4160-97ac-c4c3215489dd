# Required Permissions for CCP RIP DZ Application

## Platform-Specific Permissions

### Android Permissions (AndroidManifest.xml)
```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.INTERNET" />
```

### iOS Permissions (Info.plist)
```xml
<key>NSCameraUsageDescription</key>
<string>This app needs camera access to scan RIP codes.</string>
```

## Detailed Permission Requirements

### 1. Camera Permission
- **Android:** `android.permission.CAMERA`
- **iOS:** `NSCameraUsageDescription`
- **Purpose:** Scan QR codes containing CCP account information
- **When Requested:** When user taps "Scan QR Code" button
- **Mandatory:** No - users can manually enter account information

### 2. Internet Permission
- **Android:** `android.permission.INTERNET`
- **iOS:** Automatically granted
- **Purpose:** 
  - Google Drive API access for cloud backup
  - Google OAuth authentication
  - Future advertising features (currently disabled)
- **When Requested:** On app installation (Android)
- **Mandatory:** No for basic functionality, Yes for cloud backup

## Flutter Plugin Permissions (Implicit)

### 3. Local Storage Access
- **Plugin:** `sqflite`, `path_provider`
- **Purpose:** Store CCP account data locally in SQLite database
- **Platform:** All platforms
- **Mandatory:** Yes - core functionality

### 4. Secure Storage Access
- **Plugin:** `flutter_secure_storage`
- **Purpose:** Encrypt sensitive data like authentication tokens
- **Platform:** All platforms  
- **Mandatory:** Yes - for security

### 5. Shared Preferences
- **Plugin:** `shared_preferences`
- **Purpose:** Store app settings and preferences
- **Platform:** All platforms
- **Mandatory:** Yes - for app configuration

### 6. File System Access
- **Plugin:** `path_provider`
- **Purpose:** Access app documents directory for data storage
- **Platform:** All platforms
- **Mandatory:** Yes - for data persistence

## Google Services Permissions

### 7. Google Sign-In
- **Plugin:** `google_sign_in`
- **Purpose:** Authenticate with Google account for Drive access
- **When Requested:** When user chooses cloud backup option
- **Mandatory:** No - only for cloud backup feature

### 8. Google Drive API Access
- **Plugin:** `googleapis`
- **Purpose:** Upload/download backup files to user's Google Drive
- **Scopes Required:**
  - `https://www.googleapis.com/auth/drive.file`
- **When Requested:** During Google authentication flow
- **Mandatory:** No - only for cloud backup feature

## Optional/Future Permissions

### 9. Network State (Not currently used)
- **Android:** `android.permission.ACCESS_NETWORK_STATE`
- **Purpose:** Check internet connectivity status
- **Status:** May be added in future versions

### 10. External Storage (Not required)
- **Android:** `android.permission.WRITE_EXTERNAL_STORAGE`
- **Purpose:** Export data to external storage
- **Status:** Not needed - using app-scoped storage and sharing

## Permission Request Flow

1. **App Installation:**
   - Internet permission (Android) - automatically granted
   - No permissions requested on first launch

2. **First QR Scan:**
   - Camera permission requested with rationale dialog

3. **Google Drive Setup:**
   - Google Sign-In permission requested
   - Drive API scopes requested through OAuth flow

4. **No Background Permissions:**
   - App does not request location, microphone, or other sensitive permissions

## Privacy Compliance

- **Minimal Permissions:** Only essential permissions are requested
- **Clear Purpose:** Each permission has a clear, user-facing purpose
- **Optional Features:** Advanced features requiring sensitive permissions are optional
- **No Permanent Access:** Camera and network access only used when actively needed

## App Store Compliance

### Google Play Store
- All permissions are justified in the app description
- No sensitive permissions used without clear user benefit
- Complies with target API level requirements

### Apple App Store  
- Camera usage description provided
- No additional permissions required beyond camera access
- Follows iOS permission best practices

---

**Total Required Permissions:** 2 (Camera, Internet)  
**Optional Permissions:** 0  
**Implicit Framework Permissions:** 4 (Storage, Secure Storage, Preferences, File Access)
