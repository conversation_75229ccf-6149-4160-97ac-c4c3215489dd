# Privacy Policy - CCP RIP DZ Application

**Last updated:** September 3, 2025  
**Version:** 2.1.0  
**Contact:** <EMAIL>  
**Full Policy:** https://ccp-rip.blogspot.com/p/privacy-policy.html

## Introduction

Welcome to the CCP RIP DZ application privacy policy. This policy explains how we collect, use, and protect your information when you use our application for calculating RIP (Relevé d'Identité Postale) for CCP (Compte Chèque Postal) accounts.

## Data Collection

**Local Data Only:** Our application does not collect or transmit personally identifiable information to external servers. All CCP account data you enter is stored securely on your device using <PERSON>lutter's secure storage.

**Google Drive Integration:** When you choose to backup your data to Google Drive, the data is encrypted and stored in your personal Google Drive account. We do not have access to this data - only you can access it through your Google account.

**No Third-Party Analytics:** We do not use analytics services that track user behavior or collect personal data.

## Google Services Integration

**Google Drive API:** Used for optional cloud backup functionality. Requires your explicit consent through Google OAuth authentication.

**Google AdSense (Currently Disabled):** The app includes provisions for Google AdSense advertising but this feature is currently disabled in version 2.1.0.

## Permissions Required

Our application requests the following permissions:

### Android Permissions:
- **CAMERA** - Required to scan QR codes containing CCP account information
- **INTERNET** - Required for Google Drive backup functionality and future advertisement features

### iOS Permissions:
- **Camera Usage** - "This app needs camera access to scan RIP codes" - Required for QR code scanning functionality

### Implicit Permissions (Flutter Framework):
- **Local Storage** - To securely store your CCP account data on device
- **Secure Storage** - To encrypt sensitive account information
- **Network Access** - For Google Drive integration and potential future features

## Data Security

- **Local Encryption:** All CCP account data is stored using Flutter's secure storage with device-level encryption
- **Google Drive Security:** Backup data is protected by Google's security infrastructure and your Google account authentication
- **No Data Transmission:** Account numbers and financial data never leave your device except for encrypted Google Drive backups (if you choose this option)

## Third-Party Services

- **Google Drive API:** For optional cloud backup (requires explicit user consent)
- **Google OAuth:** For secure authentication with Google services
- **Flutter Framework:** Cross-platform development framework

## Data Retention

- **Local Data:** Stored until you delete the app or manually clear the data
- **Google Drive Backups:** Retained according to your Google Drive storage policies
- **Authentication Tokens:** Stored securely and refreshed automatically

## User Rights

You have the right to:
- Access all data stored by the application
- Delete your local data at any time
- Revoke Google Drive access through your Google account settings
- Export your data using the app's sharing functionality

## Policy Changes

We may update this privacy policy from time to time. Users will be notified of significant changes through app updates and in-app notifications.

## Contact Information

For privacy-related questions or concerns:
- **Email:** <EMAIL>
- **Full Privacy Policy:** https://ccp-rip.blogspot.com/p/privacy-policy.html

## Compliance

This application is designed to comply with:
- GDPR (General Data Protection Regulation)
- Google Play Store policies
- Apple App Store guidelines
- Algerian data protection regulations

---

**By using the CCP RIP DZ application, you agree to this privacy policy.**
