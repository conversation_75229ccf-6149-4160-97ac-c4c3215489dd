# 🚨 CORRECTIFS SÉCURITÉ URGENTS - CCP RIP

## ❌ PROBLÈMES CRITIQUES À CORRIGER

### 1. **SUPPRIMER LES SECRETS HARDCODÉS**
- [ ] Retirer les API keys de `lib/services/google_auth_service.dart`
- [ ] Supprimer les mots de passe de `KEYSTORE_SETUP.md`
- [ ] Implémenter un système de variables d'environnement

### 2. **NETTOYER LE CODE PUBLICITAIRE**
- [ ] Supprimer complètement `lib/services/ad_service.dart`
- [ ] Supprimer `lib/widgets/banner_ad_widget.dart`
- [ ] Retirer les références AdSense de `privacy_policy_screen.dart`
- [ ] Nettoyer `AndroidManifest.xml` (supprimer commentaires ads)

### 3. **SÉCURISER LES DONNÉES FINANCIÈRES**
- [ ] Ajouter encryption spécifique pour numéros CCP
- [ ] Limiter les scopes Google Drive
- [ ] Ajouter des disclaimers de sécurité

## 🔧 CORRECTIFS IMMÉDIATS

### Étape 1: Nettoyer les secrets
```bash
# Supprimer les fichiers sensibles du git
git rm --cached KEYSTORE_SETUP.md
git rm --cached android/app/ccp-rip-keystore.jks
git rm --cached android/key.properties

# Ajouter au .gitignore
echo "android/key.properties" >> .gitignore
echo "android/app/*.jks" >> .gitignore
echo "KEYSTORE_SETUP.md" >> .gitignore
```

### Étape 2: Refactoriser google_auth_service.dart
```dart
// REMPLACER le contenu actuel par :
class GoogleAuthService {
  // À récupérer depuis environment variables ou configuration sécurisée
  static String? get apiKey => const String.fromEnvironment('GOOGLE_API_KEY');
  static String? get clientId => const String.fromEnvironment('GOOGLE_CLIENT_ID');
  static String? get clientSecret => const String.fromEnvironment('GOOGLE_CLIENT_SECRET');
  
  // Validation des credentials au startup
  static bool get hasValidCredentials => 
      apiKey != null && clientId != null && clientSecret != null;
}
```

### Étape 3: Supprimer le code publicitaire
```bash
# Supprimer les fichiers ads
rm lib/services/ad_service.dart
rm lib/widgets/banner_ad_widget.dart

# Nettoyer main.dart
# Retirer: import 'services/ad_service.dart';
# Retirer: await AdService().initialize();
```

## 📋 CHECKLIST AVANT PUBLICATION

### Sécurité ✅
- [ ] Aucun secret hardcodé dans le code
- [ ] Keystore protégé et hors du repository
- [ ] API keys dans variables d'environnement
- [ ] Code publicitaire complètement supprimé

### Privacy Policy ✅
- [ ] Supprimer toute mention d'AdSense
- [ ] Clarifier le traitement des données CCP
- [ ] Ajouter sections sur l'encryption
- [ ] Mettre à jour la date de dernière modification

### Permissions ✅
- [ ] Valider que seules CAMERA et INTERNET sont requises
- [ ] Supprimer toute référence aux ads dans les descriptions
- [ ] Vérifier les scopes Google Drive (limiter à drive.file)

### Build & Test ✅
- [ ] Compiler en mode release sans erreurs
- [ ] Tester la signature APK
- [ ] Vérifier que l'app fonctionne sans les services ads
- [ ] Valider l'authentification Google Drive

## 🚀 COMMANDES DE BUILD SÉCURISÉES

### Variables d'environnement (à définir)
```bash
export GOOGLE_API_KEY="votre_cle_api_ici"
export GOOGLE_CLIENT_ID="votre_client_id_ici"
export GOOGLE_CLIENT_SECRET="votre_client_secret_ici"
```

### Build production
```bash
# Build APK
flutter build apk --release --dart-define=GOOGLE_API_KEY=$GOOGLE_API_KEY \
                             --dart-define=GOOGLE_CLIENT_ID=$GOOGLE_CLIENT_ID \
                             --dart-define=GOOGLE_CLIENT_SECRET=$GOOGLE_CLIENT_SECRET

# Build AAB (pour Play Store)
flutter build appbundle --release --dart-define=GOOGLE_API_KEY=$GOOGLE_API_KEY \
                                  --dart-define=GOOGLE_CLIENT_ID=$GOOGLE_CLIENT_ID \
                                  --dart-define=GOOGLE_CLIENT_SECRET=$GOOGLE_CLIENT_SECRET
```

## ⚠️ AVERTISSEMENT FINAL

**CES CORRECTIFS SONT OBLIGATOIRES** avant toute soumission au Play Store.
Un compte publisher suspendu est très difficile à récupérer.

**Priorité 1:** Supprimer immédiatement tous les secrets du code
**Priorité 2:** Nettoyer le code publicitaire  
**Priorité 3:** Sécuriser les données financières

---
**Date:** 5 septembre 2025  
**Statut:** CRITIQUE - Action immédiate requise
