import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/locale_provider.dart';
import '../l10n/app_localizations.dart';

import 'main_screen.dart';
import '../models/ccp_account.dart';
import '../providers/ccp_accounts_provider.dart';
import '../services/export_service.dart';
import '../widgets/app_drawer.dart';
import '../widgets/banner_ad_widget.dart';
import '../widgets/saved_account_item.dart';

class _ManageOption extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;

  const _ManageOption({
    required this.icon,
    required this.title,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              Icon(icon, size: 20),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _EditAccountDialog extends StatefulWidget {
  final CCPAccount account;
  final TextEditingController nameController;
  final AppLocalizations loc;
  final Function(String) onSave;

  const _EditAccountDialog({
    required this.account,
    required this.nameController,
    required this.loc,
    required this.onSave,
  });

  @override
  State<_EditAccountDialog> createState() => _EditAccountDialogState();
}

class _EditAccountDialogState extends State<_EditAccountDialog> {
  bool _isSaving = false;

  void _handleSave() async {
    final newName = widget.nameController.text.trim();
    if (newName.isNotEmpty && !_isSaving) {
      setState(() {
        _isSaving = true;
      });

      try {
        // Effectuer la sauvegarde d'abord
        widget.onSave(newName);

        // Puis fermer le dialogue avec un délai
        await Future.delayed(const Duration(milliseconds: 100));
        if (mounted) {
          Navigator.of(context).pop();
        }
      } catch (e) {
        debugPrint('Error saving account: $e');
        if (mounted) {
          setState(() {
            _isSaving = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        widget.loc.savedAccountsEditDialogTitle,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
      ),
      contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
      content: SizedBox(
        width: 280,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.loc.savedAccountsEditOwnerNameLabel,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: widget.nameController,
              decoration: InputDecoration(
                hintText: widget.loc.savedAccountsEditOwnerNameHint,
                filled: true,
                fillColor: const Color(0xFFFFEC33),
                border: const OutlineInputBorder(),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                isDense: true,
              ),
              style: const TextStyle(fontSize: 14),
              textCapitalization: TextCapitalization.words,
              autofocus: false,
              enabled: !_isSaving,
            ),
          ],
        ),
      ),
      actionsPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      actions: [
        TextButton(
          onPressed: _isSaving ? null : () => Navigator.of(context).pop(),
          style: TextButton.styleFrom(
            minimumSize: const Size(0, 36),
            padding: const EdgeInsets.symmetric(horizontal: 12),
            textStyle: const TextStyle(fontSize: 12),
          ),
          child: Text(widget.loc.savedAccountsDialogCancel),
        ),
        TextButton(
          onPressed: _isSaving ? null : _handleSave,
          style: TextButton.styleFrom(
            minimumSize: const Size(0, 36),
            padding: const EdgeInsets.symmetric(horizontal: 12),
            textStyle: const TextStyle(fontSize: 12),
          ),
          child: _isSaving
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : Text(widget.loc.savedAccountsDialogSave),
        ),
      ],
    );
  }
}

class SavedAccountsScreen extends StatefulWidget {
  const SavedAccountsScreen({super.key});

  @override
  State<SavedAccountsScreen> createState() => _SavedAccountsScreenState();
}

class _SavedAccountsScreenState extends State<SavedAccountsScreen> with AutomaticKeepAliveClientMixin {
  String _getLanguageDisplay(Locale? locale) {
    final languageCode = locale?.languageCode ?? 'en';
    switch (languageCode) {
      case 'ar':
        return 'ع';
      case 'fr':
        return 'Fr';
      default:
        return 'En';
    }
  }
  final _searchController = TextEditingController();
  final _exportService = ExportService();
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final loc = AppLocalizations.of(context);
    return Scaffold(
      drawer: const AppDrawer(),
      appBar: AppBar(
        leading: Builder(
          builder: (context) => IconButton(
            icon: const Icon(Icons.menu),
            onPressed: () => Scaffold.of(context).openDrawer(),
          ),
        ),
        title: Text(
          loc.savedAccountsScreenTitle,
          overflow: TextOverflow.visible, // Permet au texte de déborder si nécessaire
          style: const TextStyle(fontSize: 16), // Taille de police réduite
        ),
        titleSpacing: 0, // Réduit l'espace entre le titre et les actions
        actions: [
          IconButton(
            icon: Consumer<LocaleProvider>(
              builder: (context, localeProvider, _) => Container(
                width: 18, // Taille réduite
                height: 18, // Taille réduite
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.fromBorderSide(
                    BorderSide(color: Colors.white, width: 1.0)
                  ),
                ),
                child: Center(
                  child: Text(
                    _getLanguageDisplay(localeProvider.locale),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                      fontSize: 9, // Taille réduite
                      height: 1.0
                    ),
                  ),
                ),
              ),
            ),
            tooltip: loc.changeLanguageTooltip,
            onPressed: () {
              showLanguageSelectionDialog(context);
            },
            padding: const EdgeInsets.symmetric(horizontal: 4), // Réduit le padding horizontal
            constraints: const BoxConstraints(), // Supprime les contraintes de taille minimale
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(12, 8, 12, 12), // Augmentation du padding en bas
            child: SizedBox(
              height: 42, // Hauteur légèrement réduite par rapport à l'original
              child: TextField(
                controller: _searchController,
                style: const TextStyle(
                  fontSize: 13, // Taille de police légèrement réduite
                ),
                decoration: InputDecoration(
                  labelText: null, // Suppression du label pour gagner de l'espace
                  hintText: loc.savedAccountsSearchHint,
                  hintStyle: const TextStyle(
                    fontSize: 13, // Taille de police légèrement réduite
                  ),
                  prefixIcon: const Icon(
                    Icons.search,
                    size: 18, // Taille d'icône légèrement réduite
                  ),
                  border: const OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(8)),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12),
                  filled: true,
                  fillColor: const Color(0xFFFFEC33),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(
                            Icons.clear,
                            size: 18, // Taille d'icône légèrement réduite
                          ),
                          onPressed: () {
                            _searchController.clear();
                            context.read<CCPAccountsProvider>().clearSearch();
                          },
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                          splashRadius: 20,
                        )
                      : null,
                ),
                onChanged: (value) {
                  setState(() {}); // Pour mettre à jour l'affichage du bouton de suppression
                  context.read<CCPAccountsProvider>().searchAccounts(value);
                },
              ),
            ),
          ),
          Expanded(
            child: Consumer<CCPAccountsProvider>(
              builder: (context, provider, child) {
                final accounts = provider.filteredAccounts;
                if (accounts.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.account_balance_wallet,
                          size: 64,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          provider.isSearching
                              ? loc.savedAccountsEmptySearch
                              : loc.savedAccountsEmpty,
                          style: const TextStyle(
                            fontSize: 18,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  );
                }
                return ListView.builder(
                  padding: const EdgeInsets.fromLTRB(12, 4, 12, 16),
                  itemCount: accounts.length,
                  itemBuilder: (context, index) {
                    final account = accounts[index];
                    return SavedAccountItem(
                      account: account,
                      onDelete: (account) {
                        provider.deleteAccount(account.id!);
                      },
                      onEdit: (account) {
                        _showEditDialog(context, account, loc);
                      },
                    );
                  },
                );
              },
            ),
          ),

          // Bannière publicitaire en bas
          const AdaptiveBannerAdWidget(),
        ],
      ),
    );
  }

  void _showEditDialog(BuildContext context, CCPAccount account, AppLocalizations loc) {
    final nameController = TextEditingController(text: account.ownerName);

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (dialogContext) => _EditAccountDialog(
        account: account,
        nameController: nameController,
        loc: loc,
        onSave: (newName) => _updateAccountName(account, newName),
      ),
    ).then((_) {
      nameController.dispose();
    });
  }

  void _updateAccountName(CCPAccount account, String newName) {
    // Utiliser Future.microtask pour éviter les conflits de context
    Future.microtask(() {
      try {
        if (mounted) {
          final updatedAccount = account.copyWith(
            ownerName: newName,
            dateModified: DateTime.now(),
          );

          final accountsProvider = Provider.of<CCPAccountsProvider>(context, listen: false);
          accountsProvider.updateAccount(updatedAccount);
        }
      } catch (e) {
        debugPrint('Error updating account name: $e');
      }
    });
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    Future.microtask(() {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Theme.of(context).colorScheme.error : null,
        ),
      );
    });
  }

  Future<void> _importAccounts(BuildContext context, AppLocalizations loc) async {
    final provider = Provider.of<CCPAccountsProvider>(context, listen: false);
    try {
      final count = await _exportService.showImportDialog(context);
      if (!mounted) return;
      if (count > 0) {
        _showSnackBar(loc.savedAccountsImportSuccess(count));
        provider.loadAccounts();
      }
    } catch (e) {
      if (!mounted) return;
      _showSnackBar(loc.savedAccountsImportError(e.toString()), isError: true);
    }
  }

  Future<void> _exportAccounts(BuildContext context, AppLocalizations loc) async {
    try {
      await _exportService.exportAndShareAccounts();
      // Optionally show a success message, though sharing itself is an action.
    } catch (e) {
      if (!mounted) return;
      _showSnackBar(loc.savedAccountsExportError(e.toString()), isError: true);
    }
  }

  void _showManageDialog(BuildContext context, AppLocalizations loc) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(12),
                  child: Text(
                    loc.savedAccountsManageTitle,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Divider(height: 1),

                _ManageOption(
                  icon: Icons.file_upload,
                  title: loc.savedAccountsImportTitle,
                  onTap: () {
                    Navigator.of(context).pop();
                    _importAccounts(context, loc);
                  },
                ),
                const Divider(height: 1),
                _ManageOption(
                  icon: Icons.file_download,
                  title: loc.savedAccountsExportTitle,
                  onTap: () {
                    Navigator.of(context).pop();
                    _exportAccounts(context, loc);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
