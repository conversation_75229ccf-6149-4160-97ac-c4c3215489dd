import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'connectivity_service.dart';

class AdService {
  static final AdService _instance = AdService._internal();

  factory AdService() => _instance;

  AdService._internal();

  // IDs AdMob fournis par l'utilisateur
  static const String _appId = 'ca-app-pub-4778309361021572~2687012430';
  static const String _bannerAdUnitId = 'ca-app-pub-4778309361021572/9060849096';

  // IDs de test pour le développement
  static const String _testBannerAdUnitId = 'ca-app-pub-3940256099942544/6300978111';

  final ConnectivityService _connectivityService = ConnectivityService();
  bool _isInitialized = false;

  /// Initialise le SDK Google Mobile Ads
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialiser le service de connectivité
      await _connectivityService.initialize();

      // Initialiser le SDK Google Mobile Ads
      await MobileAds.instance.initialize();

      _isInitialized = true;
      debugPrint('AdService: SDK Google Mobile Ads initialisé avec succès');
    } catch (e) {
      debugPrint('AdService: Erreur lors de l\'initialisation: $e');
    }
  }

  /// Obtient l'ID de l'unité publicitaire bannière
  String get bannerAdUnitId {
    // Utiliser l'ID de test en mode debug, l'ID réel en production
    return kDebugMode ? _testBannerAdUnitId : _bannerAdUnitId;
  }

  /// Vérifie si les publicités peuvent être affichées
  Future<bool> canShowAds() async {
    if (!_isInitialized) {
      await initialize();
    }

    return await _connectivityService.checkConnectivity();
  }

  /// Crée une bannière publicitaire standard
  BannerAd? createBannerAd({
    required Function(Ad ad) onAdLoaded,
    required Function(Ad ad, LoadAdError error) onAdFailedToLoad,
  }) {
    try {
      return BannerAd(
        adUnitId: bannerAdUnitId,
        size: AdSize.banner,
        request: const AdRequest(),
        listener: BannerAdListener(
          onAdLoaded: onAdLoaded,
          onAdFailedToLoad: onAdFailedToLoad,
          onAdOpened: (Ad ad) => debugPrint('AdService: Bannière ouverte'),
          onAdClosed: (Ad ad) => debugPrint('AdService: Bannière fermée'),
        ),
      );
    } catch (e) {
      debugPrint('AdService: Erreur lors de la création de la bannière: $e');
      return null;
    }
  }

  /// Crée une bannière publicitaire adaptative
  BannerAd? createAdaptiveBannerAd({
    required BuildContext context,
    required Function(Ad ad) onAdLoaded,
    required Function(Ad ad, LoadAdError error) onAdFailedToLoad,
  }) {
    try {
      return BannerAd(
        adUnitId: bannerAdUnitId,
        size: AdSize.getCurrentOrientationAnchoredAdaptiveBannerAdSize(
          MediaQuery.of(context).size.width.truncate(),
        ) ?? AdSize.banner,
        request: const AdRequest(),
        listener: BannerAdListener(
          onAdLoaded: onAdLoaded,
          onAdFailedToLoad: onAdFailedToLoad,
          onAdOpened: (Ad ad) => debugPrint('AdService: Bannière adaptative ouverte'),
          onAdClosed: (Ad ad) => debugPrint('AdService: Bannière adaptative fermée'),
        ),
      );
    } catch (e) {
      debugPrint('AdService: Erreur lors de la création de la bannière adaptative: $e');
      return null;
    }
  }

  /// Libère les ressources d'une publicité
  void disposeBannerAd(BannerAd? ad) {
    try {
      ad?.dispose();
    } catch (e) {
      debugPrint('AdService: Erreur lors de la libération de la bannière: $e');
    }
  }

  /// Libère toutes les ressources du service
  void dispose() {
    _connectivityService.dispose();
    _isInitialized = false;
    debugPrint('AdService: Service fermé');
  }
}
