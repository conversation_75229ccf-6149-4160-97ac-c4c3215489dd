import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

/// Service pour gérer la connectivité Internet
class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  
  factory ConnectivityService() => _instance;
  
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  // Stream controller pour notifier les changements de connectivité
  final StreamController<bool> _connectivityController = StreamController<bool>.broadcast();
  
  bool _isConnected = false;
  bool _isInitialized = false;

  /// Stream pour écouter les changements de connectivité
  Stream<bool> get connectivityStream => _connectivityController.stream;

  /// Getter pour l'état actuel de la connectivité
  bool get isConnected => _isConnected;

  /// Initialise le service de connectivité
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Vérifier l'état initial de la connectivité
      await _checkConnectivity();
      
      // Écouter les changements de connectivité
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        _onConnectivityChanged,
        onError: (error) {
          debugPrint('ConnectivityService: Erreur lors de l\'écoute de la connectivité: $error');
        },
      );
      
      _isInitialized = true;
      debugPrint('ConnectivityService: Service initialisé avec succès');
    } catch (e) {
      debugPrint('ConnectivityService: Erreur lors de l\'initialisation: $e');
    }
  }

  /// Vérifie l'état actuel de la connectivité
  Future<bool> checkConnectivity() async {
    await _checkConnectivity();
    return _isConnected;
  }

  /// Méthode privée pour vérifier la connectivité
  Future<void> _checkConnectivity() async {
    try {
      final List<ConnectivityResult> connectivityResults = await _connectivity.checkConnectivity();
      _updateConnectivityStatus(connectivityResults);
    } catch (e) {
      debugPrint('ConnectivityService: Erreur lors de la vérification de la connectivité: $e');
      _updateConnectivityStatus([ConnectivityResult.none]);
    }
  }

  /// Callback appelé lors des changements de connectivité
  void _onConnectivityChanged(List<ConnectivityResult> results) {
    _updateConnectivityStatus(results);
  }

  /// Met à jour le statut de connectivité
  void _updateConnectivityStatus(List<ConnectivityResult> results) {
    final bool wasConnected = _isConnected;
    
    // Considérer comme connecté si au moins une connexion est disponible
    _isConnected = results.any((result) => 
      result == ConnectivityResult.mobile ||
      result == ConnectivityResult.wifi ||
      result == ConnectivityResult.ethernet ||
      result == ConnectivityResult.vpn
    );

    // Notifier seulement si l'état a changé
    if (wasConnected != _isConnected) {
      debugPrint('ConnectivityService: État de connectivité changé: $_isConnected');
      _connectivityController.add(_isConnected);
    }
  }

  /// Libère les ressources
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivityController.close();
    _isInitialized = false;
    debugPrint('ConnectivityService: Service fermé');
  }
}
