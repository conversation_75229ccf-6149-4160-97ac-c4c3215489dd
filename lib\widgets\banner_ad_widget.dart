import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import '../services/ad_service.dart';

class BannerAdWidget extends StatefulWidget {
  const BannerAdWidget({super.key});

  @override
  State<BannerAdWidget> createState() => _BannerAdWidgetState();
}

class _BannerAdWidgetState extends State<BannerAdWidget> {
  final AdService _adService = AdService();
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;
  bool _canShowAds = false;

  @override
  void initState() {
    super.initState();
    _initializeAd();
  }

  Future<void> _initializeAd() async {
    // Vérifier si les publicités peuvent être affichées
    _canShowAds = await _adService.canShowAds();

    if (_canShowAds && mounted) {
      _loadBannerAd();
    }
  }

  void _loadBannerAd() {
    _bannerAd = _adService.createBannerAd(
      onAdLoaded: (ad) {
        if (mounted) {
          setState(() {
            _isAdLoaded = true;
          });
        }
      },
      onAdFailedToLoad: (ad, error) {
        debugPrint('BannerAdWidget: Échec du chargement de la bannière: $error');
        ad.dispose();
        if (mounted) {
          setState(() {
            _isAdLoaded = false;
          });
        }
      },
    );

    _bannerAd?.load();
  }

  @override
  void dispose() {
    _adService.disposeBannerAd(_bannerAd);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_canShowAds || !_isAdLoaded || _bannerAd == null) {
      return const SizedBox.shrink();
    }

    return Container(
      alignment: Alignment.center,
      width: _bannerAd!.size.width.toDouble(),
      height: _bannerAd!.size.height.toDouble(),
      child: AdWidget(ad: _bannerAd!),
    );
  }
}

class AdaptiveBannerAdWidget extends StatefulWidget {
  const AdaptiveBannerAdWidget({super.key});

  @override
  State<AdaptiveBannerAdWidget> createState() => _AdaptiveBannerAdWidgetState();
}

class _AdaptiveBannerAdWidgetState extends State<AdaptiveBannerAdWidget> {
  final AdService _adService = AdService();
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;
  bool _canShowAds = false;

  @override
  void initState() {
    super.initState();
    _initializeAd();
  }

  Future<void> _initializeAd() async {
    // Vérifier si les publicités peuvent être affichées
    _canShowAds = await _adService.canShowAds();

    if (_canShowAds && mounted) {
      _loadAdaptiveBannerAd();
    }
  }

  Future<void> _loadAdaptiveBannerAd() async {
    _bannerAd = await _adService.createAdaptiveBannerAd(
      context: context,
      onAdLoaded: (ad) {
        if (mounted) {
          setState(() {
            _isAdLoaded = true;
          });
        }
      },
      onAdFailedToLoad: (ad, error) {
        debugPrint('AdaptiveBannerAdWidget: Échec du chargement de la bannière adaptative: $error');
        ad.dispose();
        if (mounted) {
          setState(() {
            _isAdLoaded = false;
          });
        }
      },
    );

    _bannerAd?.load();
  }

  @override
  void dispose() {
    _adService.disposeBannerAd(_bannerAd);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_canShowAds || !_isAdLoaded || _bannerAd == null) {
      return const SizedBox.shrink();
    }

    return Container(
      alignment: Alignment.center,
      width: _bannerAd!.size.width.toDouble(),
      height: _bannerAd!.size.height.toDouble(),
      child: AdWidget(ad: _bannerAd!),
    );
  }
}
