import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../l10n/app_localizations.dart';
import '../screens/local_backup_screen.dart';
import '../screens/main_screen.dart';
import '../screens/privacy_policy_screen.dart';

class FastAppDrawer extends StatelessWidget {
  const FastAppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context);
    final theme = Theme.of(context);
    
    return Drawer(
      child: Column(
        children: [
          // En-tête simplifié et optimisé
          _buildHeader(context, theme),
          
          // Menu items optimisés
          Expanded(
            child: _buildMenuItems(context, loc, theme),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, ThemeData theme) {
    return Container(
      width: double.infinity,
      height: 140,
      padding: const EdgeInsets.only(top: 50, bottom: 16),
      decoration: BoxDecoration(
        color: theme.primaryColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Logo optimisé avec taille fixe
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              Icons.account_balance,
              size: 32,
              color: theme.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          // Titre simplifié
          const Text(
            'CCP RIP DZ',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItems(BuildContext context, AppLocalizations loc, ThemeData theme) {
    return ListView(
      padding: const EdgeInsets.symmetric(vertical: 8),
      children: [
        // Navigation principale
        _MenuItem(
          icon: Icons.privacy_tip_outlined,
          title: loc.menuPrivacyPolicy,
          onTap: () => _navigate(context, const PrivacyPolicyScreen()),
        ),
        _MenuItem(
          icon: Icons.save_alt_outlined,
          title: loc.menuLocalBackup,
          onTap: () => _navigate(context, const LocalBackupScreen()),
        ),
        
        // Divider léger
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Divider(height: 1, thickness: 0.5),
        ),
        
        // Actions d'app
        _MenuItem(
          icon: Icons.star_outline,
          title: loc.menuRateApp,
          onTap: () => _handleAction(context, AppMenuChoice.rate),
        ),
        _MenuItem(
          icon: Icons.system_update_outlined,
          title: loc.menuUpdate,
          onTap: () => _handleAction(context, AppMenuChoice.update),
        ),
        _MenuItem(
          icon: Icons.info_outline,
          title: loc.menuAbout,
          onTap: () => _handleAction(context, AppMenuChoice.about),
        ),
        
        // Divider avant quit
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Divider(height: 1, thickness: 0.5),
        ),
        
        // Quit avec couleur rouge
        _MenuItem(
          icon: Icons.exit_to_app_outlined,
          title: loc.menuQuit,
          onTap: () {
            Navigator.pop(context);
            SystemNavigator.pop();
          },
          textColor: Colors.red,
          iconColor: Colors.red,
        ),
      ],
    );
  }

  void _navigate(BuildContext context, Widget screen) {
    Navigator.pop(context);
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => screen),
    );
  }

  void _handleAction(BuildContext context, AppMenuChoice action) {
    Navigator.pop(context);
    handleAppMenuChoice(context, action);
  }
}

class _MenuItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final Color? textColor;
  final Color? iconColor;

  const _MenuItem({
    required this.icon,
    required this.title,
    required this.onTap,
    this.textColor,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveTextColor = textColor ?? theme.textTheme.bodyMedium?.color;
    final effectiveIconColor = iconColor ?? theme.primaryColor;

    return ListTile(
      dense: true,
      leading: Icon(
        icon,
        size: 22,
        color: effectiveIconColor,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: effectiveTextColor,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 2),
    );
  }
}
